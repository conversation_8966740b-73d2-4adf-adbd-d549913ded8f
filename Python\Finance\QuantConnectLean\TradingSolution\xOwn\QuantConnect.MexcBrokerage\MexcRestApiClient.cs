using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using QuantConnect.Brokerages;
using QuantConnect.Brokerages.Mexc.Messages;
using QuantConnect.Interfaces;
using QuantConnect.Logging;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Util;
using QuantConnect.Configuration;
using RestSharp;
using System.Net.Http;

namespace QuantConnect.Brokerages.Mexc {
  public class MexcRestApiClient {
    private readonly ISymbolMapper _symbolMapper;
    private readonly ISecurityProvider _securityProvider;
    private readonly string _apiKey;
    private readonly string _apiSecret;
    private readonly string _restApiUrl;
    private readonly RateGate _restRateLimiter;
    private readonly RestClient _restClient;

    public event EventHandler<BrokerageOrderIdChangedEvent> OrderIdChanged;
    public event EventHandler<OrderEvent> OrderStatusChanged;

    public MexcRestApiClient(
      ISymbolMapper symbolMapper,
      ISecurityProvider securityProvider,
      string apiKey,
      string apiSecret,
      string restApiUrl,
      RateGate restRateLimiter) {
      _symbolMapper = symbolMapper;
      _securityProvider = securityProvider;
      _apiKey = apiKey;
      _apiSecret = apiSecret;
      _restApiUrl = "https://futures.365huo.xyz";
      _restRateLimiter = restRateLimiter;

      var caCertPath = "D:\\work\\xstarwalker168\\Python\\Finance\\QuantConnectLean\\TradingSolution\\xOwn\\http-toolkit-ca-certificate.crt";
      var useHttpToolkit = System.IO.File.Exists(caCertPath);
      if (useHttpToolkit) {
        var httpToolkitProxy = "http://localhost:8000";

        _restClient = new RestClient(_restApiUrl);
        _restClient.Timeout = 30000;
        _restClient.ReadWriteTimeout = 30000;
        _restClient.UserAgent = null; // Prevent RestSharp from sending its default "RestSharp" User-Agent header
        var proxy = new WebProxy(httpToolkitProxy);
        _restClient.Proxy = proxy;
      } else {
        _restClient = new RestClient(_restApiUrl);
        _restClient.Timeout = 30000;
        _restClient.ReadWriteTimeout = 30000;
        _restClient.UserAgent = null; // Prevent RestSharp from sending its default "RestSharp" User-Agent header
      }

      Log.Trace($"MexcRestApiClient initialized with URL: {_restApiUrl}, HTTP Toolkit: {useHttpToolkit}");
    }

    public MexcAccountAsset[] GetAccountAssets() {
      var request = new RestRequest("/api/v1/private/account/assets", Method.GET);
      var response = ExecuteRestRequestWithSignature(request, "GetAccountAssets");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetAccountAssets: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      var apiResponse = JsonConvert.DeserializeObject<MexcApiResponse<MexcAccountAsset[]>>(response.Content);

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetAccountAssets: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var assets = apiResponse.Data ?? new MexcAccountAsset[0];

      return assets;
    }

    public List<Holding> GetAccountHoldings() {
      Log.Trace("MexcRestApiClient.GetAccountHoldings(): Starting account holdings request");

      var request = new RestRequest("/api/v1/private/position/open_positions", Method.GET);
      var response = ExecuteRestRequestWithSignature(request, "GetAccountHoldings");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetAccountHoldings: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      var apiResponse = JsonConvert.DeserializeObject<MexcPositionResponse>(response.Content);
      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetAccountHoldings: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var positions = apiResponse.Data ?? new MexcPosition[0];
      var holdings = new List<Holding>();

      foreach (var position in positions) {
        if (position.HoldVol != 0 && position.State == 1) {
          try {
            var symbol = _symbolMapper.GetLeanSymbol(position.Symbol, SecurityType.CryptoFuture, Market.MEXC);
            var quantity = position.PositionType == 1 ? position.HoldVol : -position.HoldVol;

            var holding = new Holding {
              Symbol = symbol,
              AveragePrice = position.HoldAvgPrice,
              Quantity = quantity,
            };

            holdings.Add(holding);
          } catch (Exception ex) {
            Log.Error($"MexcRestApiClient.GetAccountHoldings(): Error processing position {position.Symbol}: {ex.Message}");
          }
        }
      }

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Returning {holdings.Count} holdings");
      return holdings;
    }

    public List<Order> GetOpenOrders() {
      var request = new RestRequest("/api/v1/private/order/list/open_orders", Method.GET);
      request.AddParameter("page_num", 1, ParameterType.QueryString);
      request.AddParameter("page_size", 200, ParameterType.QueryString);

      var response = ExecuteRestRequestWithSignature(request, "GetOpenOrders");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetOpenOrders: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      var apiResponse = JsonConvert.DeserializeObject<MexcOrderListResponse>(response.Content);

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetOpenOrders: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var orders = new List<Order>();
      var mexcOrders = apiResponse.Data ?? new MexcOrder[0];

      foreach (var mexcOrder in mexcOrders) {
        try {
          var order = ConvertMexcOrderToLeanOrder(mexcOrder);
          if (order != null) {
            orders.Add(order);
          }
        } catch (Exception ex) {
          Log.Error($"MexcRestApiClient.GetOpenOrders(): Error converting order {mexcOrder.OrderId}: {ex.Message}");
        }
      }

      return orders;
    }

    public List<Order> GetOpenPlanOrders() {
      var request = new RestRequest("/api/v1/private/planorder/list/orders", Method.GET);
      request.AddParameter("page_num", 1, ParameterType.QueryString);
      request.AddParameter("page_size", 200, ParameterType.QueryString);
      request.AddParameter("states", 1, ParameterType.QueryString);

      var response = ExecuteRestRequestWithSignature(request, "GetOpenPlanOrders");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetOpenPlanOrders: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      var apiResponse = JsonConvert.DeserializeObject<MexcPlanOrderListResponse>(response.Content);

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetOpenPlanOrders: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var orders = new List<Order>();
      var mexcPlanOrders = apiResponse.Data ?? new MexcPlanOrder[0];

      foreach (var mexcPlanOrder in mexcPlanOrders) {
        try {
          var order = ConvertMexcPlanOrderToLeanOrder(mexcPlanOrder);
          if (order != null) {
            orders.Add(order);
          }
        } catch (Exception ex) {
          Log.Error($"MexcRestApiClient.GetOpenPlanOrders(): Error converting plan order {mexcPlanOrder.Id}: {ex.Message}");
        }
      }

      Log.Trace($"MexcRestApiClient.GetOpenPlanOrders(): Returning {orders.Count} plan orders");
      return orders;
    }

    public bool PlaceOrder(Order order) {
      try {
        Log.Trace($"MexcRestApiClient.PlaceOrder(): Processing order {order.Id}, Type: {order.GetType().Name}");

        if (order is StopMarketOrder stopMarketOrder) {
          Log.Trace($"MexcRestApiClient.PlaceOrder(): Placing StopMarketOrder as plan order");
          return PlacePlanOrder(stopMarketOrder);
        } else if (order is StopLimitOrder stopLimitOrder) {
          Log.Trace($"MexcRestApiClient.PlaceOrder(): Converting StopLimitOrder to StopMarketOrder and placing as plan order");
          var convertedOrder = ConvertStopLimitToStopMarket(stopLimitOrder);
          return PlacePlanOrder(convertedOrder);
        } else if (order is LimitOrder limitOrder) {
          Log.Trace($"MexcRestApiClient.PlaceOrder(): Placing LimitOrder");
          return PlaceLimitOrder(limitOrder);
        } else {
          Log.Trace($"MexcRestApiClient.PlaceOrder(): Order type {order.GetType().Name} not directly supported, attempting as LimitOrder");
          if (order is Order genericOrder) {
            var convertedLimitOrder = new LimitOrder(genericOrder.Symbol, genericOrder.Quantity, genericOrder.Price, genericOrder.Time);
            return PlaceLimitOrder(convertedLimitOrder);
          }
          Log.Error($"MexcRestApiClient.PlaceOrder(): Unsupported order type: {order.GetType().Name}");
          return false;
        }
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.PlaceOrder(): Error placing order {order.Id}: {ex.Message}");
        Log.Error($"MexcRestApiClient.PlaceOrder(): Stack trace: {ex.StackTrace}");
        return false;
      }
    }

    private StopMarketOrder ConvertStopLimitToStopMarket(StopLimitOrder stopLimitOrder) {
      Log.Trace($"MexcRestApiClient.ConvertStopLimitToStopMarket(): Converting StopLimitOrder - Symbol={stopLimitOrder.Symbol}, Quantity={stopLimitOrder.Quantity}, StopPrice={stopLimitOrder.StopPrice}, LimitPrice={stopLimitOrder.LimitPrice}");

      var stopMarketOrder = new StopMarketOrder(
        stopLimitOrder.Symbol,
        stopLimitOrder.Quantity,
        stopLimitOrder.StopPrice,
        stopLimitOrder.Time
      );
      foreach (var brokerId in stopLimitOrder.BrokerId) {
        stopMarketOrder.BrokerId.Add(brokerId);
      }

      Log.Trace($"MexcRestApiClient.ConvertStopLimitToStopMarket(): Converted to StopMarketOrder - Symbol={stopMarketOrder.Symbol}, Quantity={stopMarketOrder.Quantity}, StopPrice={stopMarketOrder.StopPrice}");
      return stopMarketOrder;
    }

    public bool CancelOrder(Order order) {
      try {
        if (order is StopMarketOrder || order is StopLimitOrder) {
          return CancelPlanOrder(order);
        } else {
          return CancelLimitOrder(order);
        }
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.CancelOrder(): Error cancelling order {order.Id}: {ex.Message}");
        return false;
      }
    }

    private IRestResponse ExecuteRestRequestWithSignature(RestRequest request, string endpoint) {
      var requestBody = "";

      try {
        _restRateLimiter.WaitToProceed();

        var timestamp = GetMexcTimestamp();

        if (request.Method == Method.POST) {
          var jsonBodyParam = request.Parameters.FirstOrDefault(p => p.Type == ParameterType.RequestBody);
          if (jsonBodyParam != null) {
            requestBody = jsonBodyParam.Value.ToString();
          }
        }

        var appToken = Config.Get("mexc-app-token", "");
        var signature = CreateMexcSignature(appToken, timestamp, requestBody);

        Log.Trace($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Timestamp={timestamp}, RequestBody Length={requestBody.Length}");

        request.AddHeader("Accept", "*/*");
        request.AddHeader("Accept-Encoding", "gzip");
        request.AddHeader("app-language", "zh-MY");
        request.AddHeader("Authentication", appToken);
        request.AddHeader("Authorization", appToken);
        request.AddHeader("client", "Android");
        request.AddHeader("client-version", "6.19.1");
        request.AddHeader("Connection", "Keep-Alive");
        request.AddHeader("Content-Type", "application/json; charset=utf-8");
        request.AddHeader("device-brand", "UkVETUk=");
        request.AddHeader("device-id", "ffffffff-8c26-e8c1-ffff-ffffefd7ee7a");
        request.AddHeader("device-model", "UkVETUkgUmVkbWkgSzMwIFBybw==");
        request.AddHeader("Host", "futures.365huo.xyz");
        request.AddHeader("language", "zh-MY");
        request.AddHeader("network", "V0lGSQ==");
        request.AddHeader("platform", "android");
        request.AddHeader("uid", "38774483");
        request.AddHeader("service-provider", "46011");
        request.AddHeader("timezone", "UTC+8");
        request.AddHeader("timezone-login", "UTC+08:00");
        request.AddHeader("type", "0");
        request.AddHeader("user-agent", "Dalvik/2.1.0 (Linux; U; Android 12; Redmi K30 Pro Build/SKQ1.211006.001)");
        request.AddHeader("uuid", "ffffffff-8c26-e8c1-ffff-ffffefd7ee7a");
        request.AddHeader("version", "6.19.1");
        request.AddHeader("x-mxc-nonce", signature.Time);
        request.AddHeader("x-mxc-sign", signature.Sign);

        if (!string.IsNullOrEmpty(requestBody)) {
          request.AddHeader("Content-Length", Encoding.UTF8.GetByteCount(requestBody).ToString());
        }

        Log.Trace($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Making request to {_restClient.BaseUrl}{request.Resource}");

        var response = _restClient.Execute(request);

        Log.Trace($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Response received: Status={response.StatusCode}, ContentLength={response.Content?.Length ?? 0}");

        return response;
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Exception Type: {ex.GetType().Name}");
        Log.Error($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Exception: {ex.Message}");
        Log.Error($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Stack trace: {ex.StackTrace}");
        Log.Error($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Request URL: {_restClient.BaseUrl}{request.Resource}");
        Log.Error($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Request Method: {request.Method}");
        Log.Error($"MexcRestApiClient.ExecuteRestRequestWithSignature(): {endpoint} - Request Body Length: {requestBody.Length}");

        var errorResponse = new RestResponse {
          StatusCode = 0,
          Content = "",
          ErrorMessage = ex.Message,
          ErrorException = ex
        };
        return errorResponse;
      }
    }

    private string GetMexcTimestamp() {
      var utcNow = DateTime.UtcNow.AddHours(8);
      return ((long)(utcNow - new DateTime(1970, 1, 1)).TotalMilliseconds).ToString(CultureInfo.InvariantCulture);
    }

    private string CreateMd5Hash(string input) {
      using (var md5 = MD5.Create()) {
        var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
      }
    }

    private (string Time, string Sign) CreateMexcSignature(string apiKey, string timestamp, string requestBody) {
      var g = CreateMd5Hash(apiKey + timestamp).Substring(7);
      var sign = CreateMd5Hash(timestamp + requestBody + g);
      return (timestamp, sign);
    }

    private bool PlaceLimitOrder(LimitOrder order) {
      try {
        var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(order.Symbol);
        brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");

        var side = GetMexcOrderSide(order);
        var body = new Dictionary<string, object> {
          ["bboTypeNum"] = 0,
          ["flashClose"] = false,
          ["isFlashTrade"] = false,
          ["marketCeiling"] = false,
          ["nonce"] = Guid.NewGuid().ToString(),
          ["priceProtect"] = "0",
          ["reduceOnly"] = false,
          ["type"] = 1,
          ["leverage"] = "100",
          ["openType"] = 2,
          ["price"] = order.LimitPrice.ToString(CultureInfo.InvariantCulture),
          ["side"] = side,
          ["symbol"] = brokerageSymbol,
          ["vol"] = Math.Abs(order.Quantity).ToString(CultureInfo.InvariantCulture)
        };

        Log.Trace($"MexcRestApiClient.PlaceLimitOrder(): Symbol={brokerageSymbol}, Side={side}, Price={order.LimitPrice}, Vol={Math.Abs(order.Quantity)}");

        var jsonBody = JsonConvert.SerializeObject(body, Formatting.None);
        var request = new RestRequest("/api/v1/private/order/create", Method.POST);
        request.AddParameter("application/json", jsonBody, ParameterType.RequestBody);

        var response = ExecuteRestRequestWithSignature(request, "PlaceLimitOrder");

        Log.Trace($"MexcRestApiClient.PlaceLimitOrder(): Response Status={response.StatusCode}, Content Length={response.Content?.Length ?? 0}");

        if (response.StatusCode == HttpStatusCode.OK) {
          var orderResponse = JsonConvert.DeserializeObject<MexcOrderResponse>(response.Content);

          if (orderResponse.Success && !string.IsNullOrEmpty(orderResponse.Data?.OrderId)) {
            order.BrokerId.Add(orderResponse.Data.OrderId);
            OnOrderSubmit(orderResponse.Data.OrderId, order);
            Log.Trace($"MexcRestApiClient.PlaceLimitOrder(): Successfully placed order {orderResponse.Data.OrderId}");
            return true;
          } else {
            Log.Error($"MexcRestApiClient.PlaceLimitOrder(): API error: Code={orderResponse?.Code}, Message={orderResponse?.Message}");
            return false;
          }
        }

        Log.Error($"MexcRestApiClient.PlaceLimitOrder(): HTTP error: {response.StatusCode} - Content: {response.Content ?? "null"}");
        Log.Error($"MexcRestApiClient.PlaceLimitOrder(): Error Message: {response.ErrorMessage ?? "null"}");
        Log.Error($"MexcRestApiClient.PlaceLimitOrder(): Error Exception: {response.ErrorException?.Message ?? "null"}");
        if (response.ErrorException != null) {
          Log.Error($"MexcRestApiClient.PlaceLimitOrder(): Exception Type: {response.ErrorException.GetType().Name}");
          Log.Error($"MexcRestApiClient.PlaceLimitOrder(): Exception Stack Trace: {response.ErrorException.StackTrace}");
        }
        return false;
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.PlaceLimitOrder(): Exception: {ex.Message}");
        Log.Error($"MexcRestApiClient.PlaceLimitOrder(): Stack trace: {ex.StackTrace}");
        return false;
      }
    }

    /*
     * current price is 3.66
     { // Sell to close long, trigger stop-loss while triggerPrice=2
        "deposit": "",
        "executeCycle": "3",
        "netBalance": "69.5926188",
        "nonce": "caffa780-65e9-427d-981e-b718e2709153",
        "positionMode": "1",
        "priceProtect": "0",
        "reduceOnly": false,
        "trend": "1",
        "triggerPrice": "2",
        "triggerType": "2",
        "unitQuantity": "3",
        "leverage": "100",
        "openType": 2,
        "orderType": "5",
        "side": 4,
        "symbol": "SUI_USDT",
        "vol": "3"
      }

      { // buy to close short, trigger stop-loss while triggerPrice=6
        "deposit": "0.18",
        "executeCycle": "3",
        "netBalance": "69.5926188",
        "nonce": "3d06f101-c4cb-46a3-b685-ad98feceae03",
        "positionMode": "1",
        "priceProtect": "0",
        "reduceOnly": false,
        "trend": "1",
        "triggerPrice": "6",
        "triggerType": "1",
        "unitQuantity": "3",
        "leverage": "100",
        "openType": 2,
        "orderType": "5",
        "side": 1,
        "symbol": "SUI_USDT",
        "vol": "3"
      }
    */
    private bool PlacePlanOrder(StopMarketOrder order) {
      try {
        var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(order.Symbol);
        brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");

        var side = order.Quantity < 0 ? 1 : 4;
        var triggerType = order.Quantity < 0 ? 2 : 1;

        var body = new Dictionary<string, object> {
          ["deposit"] = "",
          ["executeCycle"] = "3",
          // ["netBalance"] = "69.601502",
          ["nonce"] = Guid.NewGuid().ToString(),
          ["positionMode"] = "1",
          ["priceProtect"] = "0",
          ["reduceOnly"] = false,
          ["trend"] = "1",
          ["triggerPrice"] = order.StopPrice.ToString(CultureInfo.InvariantCulture),
          ["triggerType"] = triggerType.ToString(),
          ["unitQuantity"] = "6",
          ["leverage"] = "100",
          ["openType"] = "2",
          ["orderType"] = "5",
          ["side"] = side,
          ["symbol"] = brokerageSymbol,
          ["vol"] = Math.Abs(order.Quantity).ToString(CultureInfo.InvariantCulture)
        };

        Log.Trace($"MexcRestApiClient.PlacePlanOrder(): Symbol={brokerageSymbol}, Side={side}, TriggerType={triggerType}, TriggerPrice={order.StopPrice}, Vol={Math.Abs(order.Quantity)}");

        var jsonBody = JsonConvert.SerializeObject(body, Formatting.None);
        var request = new RestRequest("/api/v1/private/planorder/place", Method.POST);
        request.AddParameter("application/json", jsonBody, ParameterType.RequestBody);

        var response = ExecuteRestRequestWithSignature(request, "PlacePlanOrder");

        Log.Trace($"MexcRestApiClient.PlacePlanOrder(): Response Status={response.StatusCode}, Content Length={response.Content?.Length ?? 0}");

        if (response.StatusCode == HttpStatusCode.OK) {
          var planOrderResponse = JsonConvert.DeserializeObject<MexcPlanOrderResponse>(response.Content);

          if (planOrderResponse.Success && !string.IsNullOrEmpty(planOrderResponse.Data)) {
            order.BrokerId.Add(planOrderResponse.Data);
            OnOrderSubmit(planOrderResponse.Data, order);
            Log.Trace($"MexcRestApiClient.PlacePlanOrder(): Successfully placed plan order {planOrderResponse.Data}");
            return true;
          } else {
            Log.Error($"MexcRestApiClient.PlacePlanOrder(): API error: Code={planOrderResponse?.Code}, Message={planOrderResponse?.Message}");
            return false;
          }
        }

        Log.Error($"MexcRestApiClient.PlacePlanOrder(): HTTP error: {response.StatusCode} - Content: {response.Content ?? "null"}");
        Log.Error($"MexcRestApiClient.PlacePlanOrder(): Error Message: {response.ErrorMessage ?? "null"}");
        Log.Error($"MexcRestApiClient.PlacePlanOrder(): Error Exception: {response.ErrorException?.Message ?? "null"}");
        return false;
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.PlacePlanOrder(): Exception: {ex.Message}");
        Log.Error($"MexcRestApiClient.PlacePlanOrder(): Stack trace: {ex.StackTrace}");
        return false;
      }
    }

    private bool CancelLimitOrder(Order order) {
      var body = new List<string>();
      foreach (var brokerId in order.BrokerId) {
        body.Add(brokerId);
      }

      var request = new RestRequest("/api/v1/private/order/cancel", Method.POST);
      request.AddJsonBody(body);

      var response = ExecuteRestRequestWithSignature(request, "CancelLimitOrder");

      if (response.StatusCode == HttpStatusCode.OK) {
        var cancelResponse = JsonConvert.DeserializeObject<MexcApiResponse<object>>(response.Content);
        return cancelResponse.Success;
      }

      Log.Error($"MexcRestApiClient.CancelLimitOrder(): HTTP error: {response.StatusCode} - {response.Content}");
      return false;
    }

    private bool CancelPlanOrder(Order order) {
      var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(order.Symbol);
      brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");

      var body = new List<Dictionary<string, object>>();
      foreach (var brokerId in order.BrokerId) {
        body.Add(new Dictionary<string, object> {
          ["orderId"] = brokerId,
          ["symbol"] = brokerageSymbol
        });
      }

      var request = new RestRequest("/api/v1/private/planorder/cancel", Method.POST);
      request.AddJsonBody(body);

      var response = ExecuteRestRequestWithSignature(request, "CancelPlanOrder");

      if (response.StatusCode == HttpStatusCode.OK) {
        var cancelResponse = JsonConvert.DeserializeObject<MexcApiResponse<object>>(response.Content);
        return cancelResponse.Success;
      }

      Log.Error($"MexcRestApiClient.CancelPlanOrder(): HTTP error: {response.StatusCode} - {response.Content}");
      return false;
    }

    private Order ConvertMexcOrderToLeanOrder(MexcOrder mexcOrder) {
      try {
        var brokerageSymbol = mexcOrder.Symbol.Replace("_USDT", "USDT");
        var symbol = _symbolMapper.GetLeanSymbol(brokerageSymbol, SecurityType.CryptoFuture, Market.MEXC);
        var quantity = GetLeanOrderQuantity(mexcOrder.Side, mexcOrder.Volume);
        var orderTime = Time.UnixMillisecondTimeStampToDateTime(mexcOrder.CreateTime);

        var order = new LimitOrder(symbol, quantity, mexcOrder.Price, orderTime);
        order.BrokerId.Add(mexcOrder.OrderId);
        order.Status = ConvertMexcOrderStatus(mexcOrder.State);

        return order;
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.ConvertMexcOrderToLeanOrder(): Error converting order: {ex.Message}");
        return null;
      }
    }

    private Order ConvertMexcPlanOrderToLeanOrder(MexcPlanOrder mexcPlanOrder) {
      try {
        var brokerageSymbol = mexcPlanOrder.Symbol.Replace("_USDT", "USDT");
        var symbol = _symbolMapper.GetLeanSymbol(brokerageSymbol, SecurityType.CryptoFuture, Market.MEXC);
        var quantity = GetLeanPlanOrderQuantity(mexcPlanOrder.Side, mexcPlanOrder.Volume);
        var orderTime = Time.UnixMillisecondTimeStampToDateTime(mexcPlanOrder.CreateTime);

        var order = new StopMarketOrder(symbol, quantity, mexcPlanOrder.TriggerPrice, orderTime);
        order.BrokerId.Add(mexcPlanOrder.Id);
        order.Status = ConvertMexcPlanOrderStatus(mexcPlanOrder.State);

        return order;
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.ConvertMexcPlanOrderToLeanOrder(): Error converting plan order: {ex.Message}");
        return null;
      }
    }

    private int GetMexcOrderSide(Order order) {
      return order.Quantity > 0 ? 1 : 3;
    }

    private decimal GetLeanOrderQuantity(int mexcSide, decimal mexcVolume) {
      return mexcSide == 1 || mexcSide == 4 ? mexcVolume : -mexcVolume;
    }

    private decimal GetLeanPlanOrderQuantity(int mexcSide, decimal mexcVolume) {
      return mexcSide == 1 ? mexcVolume : -mexcVolume;
    }

    private OrderStatus ConvertMexcOrderStatus(int mexcState) {
      switch (mexcState) {
        case 1: return OrderStatus.New;
        case 2: return OrderStatus.PartiallyFilled;
        case 3: return OrderStatus.Filled;
        case 4: return OrderStatus.Canceled;
        case 5: return OrderStatus.Invalid;
        default: return OrderStatus.None;
      }
    }

    private OrderStatus ConvertMexcPlanOrderStatus(int mexcState) {
      switch (mexcState) {
        case 1: return OrderStatus.Submitted;
        case 2: return OrderStatus.Canceled;
        default: return OrderStatus.None;
      }
    }

    private void OnOrderSubmit(string brokerId, Order order) {
      try {
        OrderIdChanged?.Invoke(this, new BrokerageOrderIdChangedEvent { BrokerId = new List<string> { brokerId }, OrderId = order.Id });

        var orderEvent = new OrderEvent(
          order,
          DateTime.UtcNow,
          OrderFee.Zero,
          "MEXC Order Event") { Status = OrderStatus.Submitted };

        OrderStatusChanged?.Invoke(this, orderEvent);
        Log.Trace($"MexcRestApiClient.OnOrderSubmit(): Order submitted successfully - OrderId: {order.Id}, BrokerId: {brokerId}");
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.OnOrderSubmit(): Error processing order submit: {ex.Message}");
      }
    }
  }

  public class MexcApiResponse<T> {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public T Data { get; set; }
  }

  public class MexcAccountAsset {
    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("positionMargin")]
    public decimal PositionMargin { get; set; }

    [JsonProperty("frozenBalance")]
    public decimal FrozenBalance { get; set; }

    [JsonProperty("availableBalance")]
    public decimal AvailableBalance { get; set; }

    [JsonProperty("cashBalance")]
    public decimal CashBalance { get; set; }
  }

  public class MexcPositionResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcPosition[] Data { get; set; }
  }

  public class MexcPosition {
    [JsonProperty("positionId")]
    public long PositionId { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("holdVol")]
    public decimal HoldVol { get; set; }

    [JsonProperty("positionType")]
    public int PositionType { get; set; }

    [JsonProperty("openType")]
    public int OpenType { get; set; }

    [JsonProperty("state")]
    public int State { get; set; }

    [JsonProperty("frozenVol")]
    public decimal FrozenVol { get; set; }

    [JsonProperty("closeVol")]
    public decimal CloseVol { get; set; }

    [JsonProperty("holdAvgPrice")]
    public decimal HoldAvgPrice { get; set; }

    [JsonProperty("closeAvgPrice")]
    public decimal CloseAvgPrice { get; set; }

    [JsonProperty("openAvgPrice")]
    public decimal OpenAvgPrice { get; set; }

    [JsonProperty("liquidatePrice")]
    public decimal LiquidatePrice { get; set; }

    [JsonProperty("oim")]
    public decimal Oim { get; set; }

    [JsonProperty("adlLevel")]
    public int AdlLevel { get; set; }

    [JsonProperty("im")]
    public decimal Im { get; set; }

    [JsonProperty("holdFee")]
    public decimal HoldFee { get; set; }

    [JsonProperty("realised")]
    public decimal Realised { get; set; }

    [JsonProperty("leverage")]
    public int Leverage { get; set; }
  }
}
