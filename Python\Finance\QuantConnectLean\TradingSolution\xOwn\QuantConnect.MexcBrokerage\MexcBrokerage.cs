﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Timers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using QuantConnect.Brokerages.Mexc.Messages;
using QuantConnect.Configuration;
using QuantConnect.Data;
using QuantConnect.Data.Market;
using QuantConnect.Interfaces;
using QuantConnect.Logging;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Packets;
using QuantConnect.Securities;
using QuantConnect.Util;
using Timer = System.Timers.Timer;

namespace QuantConnect.Brokerages.Mexc {
  [BrokerageFactory(typeof(MexcBrokerageFactory))]
  public class MexcBrokerage: BaseWebsocketsBrokerage
    , IDataQueueHandler {
    private IAlgorithm _algorithm;
    private SymbolPropertiesDatabaseSymbolMapper _symbolMapper;
    private readonly RateGate _webSocketRateLimiter = new RateGate(1, TimeSpan.FromMilliseconds(500));
    private LiveNodePacket _job;
    private string _webSocketBaseUrl;
    private Timer _keepAliveTimer;
    private IDataAggregator _aggregator;
    private BrokerageConcurrentMessageHandler<WebSocketMessage> _messageHandler;
    protected readonly object TickLocker = new object();
    protected string MarketName { get; set; }
    private MexcRestApiClient _restApiClient;
    private string _apiKey;
    private string _apiSecret;
    private bool _isAuthenticated;

    public MexcBrokerage()
      : this("mexc") {
    }

    public MexcBrokerage(string marketName)
      : base(marketName) {
      MarketName = marketName;
    }

    public MexcBrokerage(string marketName, IDataAggregator aggregator)
      : base(marketName) {
      MarketName = marketName;
      _aggregator = aggregator;
    }

    public MexcBrokerage(string marketName, IAlgorithm algorithm, IDataAggregator aggregator, LiveNodePacket job)
      : base(marketName) {
      MarketName = marketName;
      _algorithm = algorithm;
      _aggregator = aggregator;
      _job = job;

      InitializeFromConstructor();
    }

    private void InitializeFromConstructor() {
      _webSocketBaseUrl = "wss://contract.mexc.com/edge";
      _symbolMapper = new SymbolPropertiesDatabaseSymbolMapper(MarketName);

      _apiKey = Config.Get("mexc-api-key", "");
      _apiSecret = Config.Get("mexc-api-secret", "");

      if (string.IsNullOrEmpty(_apiKey) || string.IsNullOrEmpty(_apiSecret)) {
        Log.Trace("MexcBrokerage.InitializeFromConstructor(): API key or secret not provided, some features may not work");
      }

      var restApiUrl = Config.Get("mexc-rest-api-url", "https://contract.mexc.com");
      var restRateLimiter = new RateGate(1, TimeSpan.FromSeconds(1));
      _restApiClient = new MexcRestApiClient(_symbolMapper, null, _apiKey, _apiSecret, restApiUrl, restRateLimiter);
      _messageHandler = new BrokerageConcurrentMessageHandler<WebSocketMessage>(OnDataMessage);

      base.Initialize(_webSocketBaseUrl, new WebSocketClientWrapper(), null, _apiKey, _apiSecret);

      // Set up subscription manager
      var subscriptionManager = new EventBasedDataQueueHandlerSubscriptionManager();
      subscriptionManager.SubscribeImpl += (symbols, tickType) => Subscribe(symbols);
      subscriptionManager.UnsubscribeImpl += (symbols, tickType) => Unsubscribe(symbols);
      SubscriptionManager = subscriptionManager;

      if (!IsConnected) {
        Connect();
      }
    }
    public override bool IsConnected => WebSocket?.IsOpen == true;

#region IDataQueueHandler

    public IEnumerator<BaseData> Subscribe(SubscriptionDataConfig dataConfig, EventHandler newDataAvailableHandler) {
      if (!CanSubscribe(dataConfig.Symbol)) {
        return null;
      }
      var enumerator = _aggregator.Add(dataConfig, newDataAvailableHandler);
      SubscriptionManager.Subscribe(dataConfig);
      return enumerator;
    }

    public void Unsubscribe(SubscriptionDataConfig dataConfig) {
      SubscriptionManager.Unsubscribe(dataConfig);
      _aggregator.Remove(dataConfig);
    }

    public void SetJob(LiveNodePacket job) {
      var aggregator = Composer.Instance.GetExportedValueByTypeName<IDataAggregator>(
        Config.Get("data-aggregator", "QuantConnect.Lean.Engine.DataFeeds.AggregationManager"), forceTypeNameOnExisting: false);

      SetJobInit(job, aggregator);

      if (!IsConnected) {
        Connect();
      }
    }

    public void SetAlgorithm(IAlgorithm algorithm) {
      _algorithm = algorithm;
    }

    private void SetJobInit(LiveNodePacket job, IDataAggregator aggregator) {
      _job = job;
      _aggregator = aggregator;
      _webSocketBaseUrl = "wss://contract.mexc.com/edge";
      _symbolMapper = new SymbolPropertiesDatabaseSymbolMapper(MarketName);

      _apiKey = Config.Get("mexc-api-key", "");
      _apiSecret = Config.Get("mexc-api-secret", "");
      var restApiUrl = Config.Get("mexc-rest-api-url", "https://contract.mexc.com");
      var restRateLimiter = new RateGate(20, TimeSpan.FromSeconds(2));

      _restApiClient = new MexcRestApiClient(_symbolMapper, null, _apiKey, _apiSecret, restApiUrl, restRateLimiter);
      _restApiClient.OrderIdChanged += OnOrderIdChanged;
      _restApiClient.OrderStatusChanged += OnRestApiOrderEvent;

      Initialize(_webSocketBaseUrl, new WebSocketClientWrapper(), null, null, null);
      _messageHandler = new BrokerageConcurrentMessageHandler<WebSocketMessage>(OnDataMessage);

      var subscriptionManager = new EventBasedDataQueueHandlerSubscriptionManager();
      subscriptionManager.SubscribeImpl += (symbols, tickType) => Subscribe(symbols);
      subscriptionManager.UnsubscribeImpl += (symbols, tickType) => Unsubscribe(symbols);
      SubscriptionManager = subscriptionManager;
    }

#endregion
#region Brokerage

    public override List<Order> GetOpenOrders() {
      if (_restApiClient == null) {
        return new List<Order>();
      }

      try {
        var limitOrders = _restApiClient.GetOpenOrders();
        var planOrders = _restApiClient.GetOpenPlanOrders();

        var allOrders = new List<Order>();
        allOrders.AddRange(limitOrders);
        allOrders.AddRange(planOrders);

        return allOrders;
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.GetOpenOrders(): Error retrieving open orders: {ex.Message}");
        return new List<Order>();
      }
    }

    public override List<Holding> GetAccountHoldings() {
      if (_restApiClient == null) {
        return new List<Holding>();
      }

      try {
        var holdings = _restApiClient.GetAccountHoldings();
        if (holdings.Count > 0) {
          return holdings;
        }
        return base.GetAccountHoldings(_job?.BrokerageData, _algorithm?.Securities?.Values);
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.GetAccountHoldings(): Error retrieving account holdings: {ex.Message}");
        Log.Error($"MexcBrokerage.GetAccountHoldings(): Stack trace: {ex.StackTrace}");
        return new List<Holding>();
      }
    }

    public override List<CashAmount> GetCashBalance() {
      if (_restApiClient == null) {
        return new List<CashAmount>();
      }

      try {
        var assets = _restApiClient.GetAccountAssets();
        var cashAmounts = new List<CashAmount>();

        foreach (var asset in assets) {
          if (asset.AvailableBalance > 0 || asset.FrozenBalance > 0) {
            var totalBalance = asset.AvailableBalance + asset.FrozenBalance;
            cashAmounts.Add(new CashAmount(totalBalance, asset.Currency));
          }
        }

        return cashAmounts;
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.GetCashBalance(): Error retrieving cash balance: {ex.Message}");
        Log.Error($"MexcBrokerage.GetCashBalance(): Stack trace: {ex.StackTrace}");
        return new List<CashAmount>();
      }
    }

    public override bool PlaceOrder(Order order) {
      if (_restApiClient == null) {
        Log.Error("MexcBrokerage.PlaceOrder(): REST API client not initialized");
        return false;
      }

      if (!CanSubscribe(order.Symbol)) {
        OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Warning, -1, $"Symbol is not supported {order.Symbol}"));
        return false;
      }

      try {
        return _restApiClient.PlaceOrder(order);
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.PlaceOrder(): Error placing order {order.Id}: {ex.Message}");
        OnOrderEvent(new OrderEvent(order, DateTime.UtcNow, OrderFee.Zero, "MEXC Order Event") { Status = OrderStatus.Invalid, Message = ex.Message });
        return false;
      }
    }

    public override bool UpdateOrder(Order order) {
      OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Warning, -1, "MexcBrokerage.UpdateOrder(): Order update not supported. Please cancel and re-create."));
      return false;
    }

    public override bool CancelOrder(Order order) {
      if (_restApiClient == null) {
        Log.Error("MexcBrokerage.CancelOrder(): REST API client not initialized");
        return false;
      }

      try {
        return _restApiClient.CancelOrder(order);
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.CancelOrder(): Error cancelling order {order.Id}: {ex.Message}");
        return false;
      }
    }

    public override void Connect() {
      if (IsConnected)
        return;

      if (!IsInitialized) {
        Log.Trace("MexcBrokerage.Connect(): Not initialized, skipping connection...");
        return;
      }

      Log.Trace("MexcBrokerage.Connect(): Connecting...");
      base.Connect();
      StartKeepAliveTimer();

      if (!string.IsNullOrEmpty(_apiKey) && !string.IsNullOrEmpty(_apiSecret)) {
        AuthenticateWebSocket();
      }
    }

    public override void Disconnect() {
      Log.Trace("MexcBrokerage.Disconnect(): Disconnecting...");
      _keepAliveTimer?.Stop();
      WebSocket?.Close();
    }

#endregion
    protected override void OnMessage(object sender, WebSocketMessage e) {
      _messageHandler.HandleNewMessage(e);
    }

    private void OnDataMessage(WebSocketMessage webSocketMessage) {
      var e = (WebSocketClientWrapper.TextMessage)webSocketMessage.Data;
      try {
        // Log.Trace($"MexcBrokerage.OnDataMessage(): Received WebSocket message: {e.Message}");
        var obj = JObject.Parse(e.Message);

        if (obj["channel"]?.ToString() == "pong") {
          return;
        }

        if (obj["channel"]?.ToString() == "push.deal") {
          var tradeMessage = obj.ToObject<MexcTradeMessage>();
          if (tradeMessage?.Data != null) {
            var brokerageSymbol = tradeMessage.Symbol.Replace("_USDT", "USDT");
            EmitTradeTick(
              _symbolMapper.GetLeanSymbol(brokerageSymbol, GetSupportedSecurityType(), MarketName),
              Time.UnixMillisecondTimeStampToDateTime(tradeMessage.Data.Timestamp),
              tradeMessage.Data.Price,
              tradeMessage.Data.Volume);
          }
        } else if (obj["channel"]?.ToString() == "push.ticker") {
          var tickerMessage = obj.ToObject<MexcTickerMessage>();
          if (tickerMessage?.Data != null) {
            var brokerageSymbol = tickerMessage.Symbol.Replace("_USDT", "USDT");
            EmitQuoteTick(
              _symbolMapper.GetLeanSymbol(brokerageSymbol, GetSupportedSecurityType(), MarketName),
              tickerMessage.Data.Bid1,
              tickerMessage.Data.Ask1);
          }
        } else if (obj["channel"]?.ToString() == "push.personal.order.deal") {
          var orderDealMessage = obj.ToObject<MexcOrderDealMessage>();
          if (orderDealMessage?.Data != null) {
            OnFillOrder(orderDealMessage.Data);
          }
        } else if (obj["channel"]?.ToString() == "push.personal.plan.order") {
          var planOrderMessage = obj.ToObject<MexcPlanOrderMessage>();
          if (planOrderMessage?.Data != null) {
            OnPlanOrderUpdate(planOrderMessage.Data);
          }
        } else if (obj["channel"]?.ToString() == "rs.login") {
          var loginData = obj["data"]?.ToString();
          var loginResult = loginData == "success";
          _isAuthenticated = loginResult;
          Log.Trace($"MexcBrokerage.OnDataMessage(): Login result: {loginData} -> {loginResult}");
          if (loginResult) {
            Log.Trace("MexcBrokerage.OnDataMessage(): Authentication successful, personal order events will be received automatically");
          }
        } else if (obj["channel"]?.ToString() == "rs.sub.deal") {
          Log.Trace($"MexcBrokerage.OnDataMessage(): Subscription confirmation for deal channel: {obj}");
        } else if (obj["channel"]?.ToString() == "rs.sub.ticker") {
          Log.Trace($"MexcBrokerage.OnDataMessage(): Subscription confirmation for ticker channel: {obj}");
        } else {
          Log.Trace($"MexcBrokerage.OnDataMessage(): Unknown channel: {obj["channel"]}, Full message: {e.Message}");
        }
      } catch (Exception exception) {
        OnMessage(new BrokerageMessageEvent(BrokerageMessageType.Error, -1, $"Parsing wss message failed. Data: {e.Message} Exception: {exception}"));
        Log.Error(exception);
      }
    }

    private void EmitTradeTick(Symbol symbol, DateTime time, decimal price, decimal quantity) {
      var tick = new Tick {
        Symbol = symbol,
        Value = price,
        Quantity = Math.Abs(quantity),
        Time = time,
        TickType = TickType.Trade
      };

      lock (TickLocker) {
        _aggregator.Update(tick);
      }
    }

    private void EmitQuoteTick(Symbol symbol, decimal bidPrice, decimal askPrice) {
      var tick = new Tick {
        AskPrice = askPrice,
        BidPrice = bidPrice,
        Time = DateTime.UtcNow,
        Symbol = symbol,
        TickType = TickType.Quote,
        AskSize = 0,
        BidSize = 0
      };
      tick.SetValue();

      lock (TickLocker) {
        _aggregator.Update(tick);
      }
    }

    private void OnFillOrder(MexcOrderDeal data) {
      try {
        var order = _algorithm.Transactions.GetOrdersByBrokerageId(data.OrderId)?.SingleOrDefault();
        if (order == null) {
          Log.Error($"MexcBrokerage.OnFillOrder(): order not found: {data.OrderId}");
          return;
        }

        var fillPrice = data.Price;
        var fillQuantity = order.Direction == OrderDirection.Sell ? -data.Volume : data.Volume;
        var updTime = Time.UnixMillisecondTimeStampToDateTime(data.Timestamp);
        var orderFee = OrderFee.Zero;
        if (!string.IsNullOrEmpty(data.FeeCurrency) && data.Fee > 0) {
          orderFee = new OrderFee(new CashAmount(data.Fee, data.FeeCurrency));
        }
        var status = OrderStatus.Filled;
        var orderEvent = new OrderEvent(
          order.Id, order.Symbol, updTime, status, order.Direction, fillPrice, fillQuantity, orderFee, $"Mexc Order Event {order.Direction}");

        OnOrderEvent(orderEvent);
      } catch (Exception e) {
        Log.Error(e);
        throw;
      }
    }

    private void OnPlanOrderUpdate(MexcPlanOrder data) {
      try {
        var order = _algorithm.Transactions.GetOrdersByBrokerageId(data.Id)?.SingleOrDefault();
        if (order == null) {
          Log.Error($"MexcBrokerage.OnPlanOrderUpdate(): plan order not found: {data.Id}");
          return;
        }

        var status = ConvertPlanOrderStatus(data.State);
        var updTime = Time.UnixMillisecondTimeStampToDateTime(data.UpdateTime);

        var orderEvent = new OrderEvent(order.Id, order.Symbol, updTime, status, order.Direction, 0, 0, OrderFee.Zero, $"MEXC Plan Order Event {order.Direction}");
        OnOrderEvent(orderEvent);
        Log.Trace($"MexcBrokerage.OnPlanOrderUpdate(): Plan order {data.Id} status updated to {status}");
      } catch (Exception e) {
        Log.Error(e);
        throw;
      }
    }

    private OrderStatus ConvertPlanOrderStatus(int mexcState) {
      switch (mexcState) {
        case 1: return OrderStatus.Submitted;
        case 2: return OrderStatus.Canceled;
        default: return OrderStatus.None;
      }
    }

    private void OnOrderIdChanged(object sender, BrokerageOrderIdChangedEvent e) {
      OnOrderIdChangedEvent(e);
    }

    private void OnRestApiOrderEvent(object sender, OrderEvent e) {
      OnOrderEvent(e);
    }

    protected virtual SecurityType GetSupportedSecurityType() {
      return SecurityType.CryptoFuture;
    }

    private bool CanSubscribe(Symbol symbol) {
      if (symbol.Value.IndexOfInvariant("universe", true) != -1 || symbol.IsCanonical()) {
        return false;
      }
      return symbol.SecurityType == GetSupportedSecurityType();
    }

    protected override bool Subscribe(IEnumerable<Symbol> symbols) {
      Log.Trace($"MexcBrokerage.Subscribe(): Subscribing to {symbols.Count()} symbols");
      foreach (var symbol in symbols) {
        var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(symbol);
        brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");
        Log.Trace($"MexcBrokerage.Subscribe(): Subscribing to symbol {symbol} -> {brokerageSymbol}");

        var dealSubscriptionMessage = new MexcSubscriptionMessage {
          Method = "sub.deal",
          Param = new MexcSubscriptionParam { Symbol = brokerageSymbol }
        };
        Send(dealSubscriptionMessage);

        var tickerSubscriptionMessage = new MexcSubscriptionMessage {
          Method = "sub.ticker",
          Param = new MexcSubscriptionParam { Symbol = brokerageSymbol }
        };
        Send(tickerSubscriptionMessage);
      }
      return true;
    }

    private bool Unsubscribe(IEnumerable<Symbol> symbols) {
      foreach (var symbol in symbols) {
        var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(symbol);
        brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");

        var dealUnsubscriptionMessage = new MexcSubscriptionMessage {
          Method = "unsub.deal",
          Param = new MexcSubscriptionParam { Symbol = brokerageSymbol }
        };
        Send(dealUnsubscriptionMessage);

        var tickerUnsubscriptionMessage = new MexcSubscriptionMessage {
          Method = "unsub.ticker",
          Param = new MexcSubscriptionParam { Symbol = brokerageSymbol }
        };
        Send(tickerUnsubscriptionMessage);
      }
      return true;
    }

    private void Send(object obj) {
      var json = JsonConvert.SerializeObject(obj);
      _webSocketRateLimiter.WaitToProceed();

      if (Log.DebuggingEnabled) {
        Log.Trace("MexcBrokerage.Send(): " + json);
      }
      WebSocket.Send(json);
    }

    private void StartKeepAliveTimer() {
      // https://mexcdevelop.github.io/apidocs/contract_v1_en/#websocket-api
      // If no ping is received within 1 minute, the connection will be disconnected. It is recommended to send a ping for 10-20 seconds
      _keepAliveTimer = new Timer(15000);
      _keepAliveTimer.Elapsed += (sender, e) =>
      {
        if (IsConnected) {
          Send(new MexcPingMessage());
        }
      };
      _keepAliveTimer.Start();
    }

    private void AuthenticateWebSocket() {
      try {
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(CultureInfo.InvariantCulture);
        var signature = CreateSignature(_apiKey + timestamp, _apiSecret);

        var loginMessage = new MexcLoginMessage {
          Param = new MexcLoginParam {
            ApiKey = _apiKey,
            ReqTime = timestamp,
            Signature = signature
          }
        };

        Send(loginMessage);
        Log.Trace("MexcBrokerage.AuthenticateWebSocket(): Login message sent");
      } catch (Exception ex) {
        Log.Error($"MexcBrokerage.AuthenticateWebSocket(): Error during authentication: {ex.Message}");
      }
    }



    private string CreateSignature(string message, string secret) {
      using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret))) {
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(message));
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
      }
    }

    public override IEnumerable<BaseData> GetHistory(Data.HistoryRequest request) {
      return Enumerable.Empty<BaseData>();
    }
  }
}